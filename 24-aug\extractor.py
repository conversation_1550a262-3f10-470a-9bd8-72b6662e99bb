import pandas as pd

# Get the file names from the user
file1 = input("Enter the name of the first CSV file: ")
file2 = input("Enter the name of the second CSV file: ")

try:
    # Load the two csv files
    df1 = pd.read_csv(file1)
    df2 = pd.read_csv(file2)

    # Get the emails from the second dataframe
    emails2 = set(df2['Email'])

    # Filter the first dataframe to keep only the rows with emails not in the second file
    result_df = df1[~df1['Email'].isin(emails2)]

    # Save the result to a new csv file
    result_df.to_csv('result2.csv', index=False)

    print("The resulting data has been saved to result.csv")
    print(f"The result has {len(result_df)} rows and {len(result_df.columns)} columns.")

except FileNotFoundError:
    print("One of the files was not found. Please make sure the file names are correct and include the .csv extension.")
except KeyError:
    print("The 'Email' column was not found in one of the files. Please make sure the column name is correct.")
except Exception as e:
    print(f"An error occurred: {e}")