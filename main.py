import sys
import csv
import json
import smtplib
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr

import html2text
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTabWidget, QLabel, QLineEdit,
                             QTextEdit, QPushButton, QFileDialog, QMessageBox,
                             QPlainTextEdit, QSplitter, QGroupBox, QDialog)
from PyQt5.QtWebEngineWidgets import QWebEngineView

# --- Configuration File ---
CONFIG_FILE = 'config.json'

class EmailWorker(QThread):
    """
    Worker thread for sending emails to prevent GUI from freezing.
    """
    log_update = pyqtSignal(str)
    finished = pyqtSignal(str)

    def __init__(self, settings, recipients_data, email_data, cc_list=None, bcc_list=None):
        super().__init__()
        self.settings = settings
        self.recipients_data = recipients_data
        self.email_data = email_data
        self.cc_list = cc_list if cc_list else []
        self.bcc_list = bcc_list if bcc_list else []
        self.is_running = True

    def run(self):
        try:
            with open('template.html', 'r', encoding='utf-8') as f:
                html_template = f.read()
        except FileNotFoundError:
            self.finished.emit("Error: 'template.html' not found. Please ensure it is in the same directory.")
            return

        successful_sends = 0
        failed_sends = 0
        
        all_recipients = [data.get('Email') for data in self.recipients_data] + self.cc_list + self.bcc_list

        try:
            server = smtplib.SMTP(self.settings['host'], int(self.settings['port']))
            server.starttls()
            server.login(self.settings['username'], self.settings['password'])
            
            total = len(self.recipients_data)
            for i, recipient_data in enumerate(self.recipients_data):
                if not self.is_running:
                    break

                email_to = recipient_data.get('Email')
                if not email_to:
                    self.log_update.emit(f"[FAIL] Skipping row {i+1} - 'Email' column missing or empty.")
                    failed_sends += 1
                    continue
                
                self.log_update.emit(f"[{i+1}/{total}] Preparing email for {email_to}...")

                # Personalize subject and body
                personalized_subject = self.email_data['subject']
                personalized_body = self.email_data['body']
                for key, value in recipient_data.items():
                    placeholder = f"{{{key}}}"
                    personalized_subject = personalized_subject.replace(placeholder, str(value))
                    personalized_body = personalized_body.replace(placeholder, str(value))
                
                # Construct the email
                msg = MIMEMultipart('alternative')
                msg['Subject'] = personalized_subject
                msg['From'] = formataddr((self.email_data['from_name'], self.email_data['from_email']))
                msg['To'] = email_to
                if self.cc_list:
                    msg['Cc'] = ", ".join(self.cc_list)
                # add reply to
                reply_to_mail = "<EMAIL>"
                msg.add_header('Reply-To', reply_to_mail)
                # Create HTML and plain text parts
                final_html = html_template.format(subject=personalized_subject, message_body=personalized_body.replace('\n', '<br>'))
                plain_text = html2text.html2text(final_html)

                msg.attach(MIMEText(plain_text, 'plain'))
                msg.attach(MIMEText(final_html, 'html'))
                
                try:
                    server.sendmail(self.email_data['from_email'], [email_to] + self.cc_list + self.bcc_list, msg.as_string())
                    self.log_update.emit(f"[{i+1}/{total}] SUCCESS: Email sent to {email_to}")
                    successful_sends += 1
                except Exception as e:
                    self.log_update.emit(f"[{i+1}/{total}] FAIL: Could not send to {email_to} - {e}")
                    failed_sends += 1
                
                time.sleep(0.5) # Small delay to avoid being flagged as spam

            server.quit()
            result_message = f"Finished. Successful: {successful_sends}, Failed: {failed_sends}"
            self.finished.emit(result_message)

        except Exception as e:
            self.finished.emit(f"A critical error occurred: {e}")

    def stop(self):
        self.is_running = False

class TestEmailDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Send Test Email")
        self.setFixedSize(300, 100)
        self.email_address = ""
        
        layout = QVBoxLayout(self)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter test email address")
        layout.addWidget(self.email_input)
        
        send_button = QPushButton("Send")
        send_button.clicked.connect(self.accept)
        layout.addWidget(send_button)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Leon Marcel - Email Sender")
        self.setGeometry(100, 100, 1200, 800)
        self.recipients_data = []
        self.current_preview_index = 0
        self.full_recipient_list = set()
        self.loaded_csv_paths = []

        # --- Main Layout ---
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)

        self.create_compose_tab()
        self.create_recipients_tab()
        self.create_settings_tab()
        self.create_log_tab()
        
        self.load_settings()

    def create_compose_tab(self):
        tab_compose = QWidget()
        main_layout = QVBoxLayout(tab_compose)
        
        # --- Top UI (Sender & CC/BCC) ---
        top_layout = QHBoxLayout()
        self.from_name = QLineEdit()
        self.from_email = QLineEdit()
        top_layout.addWidget(QLabel("From Name:"))
        top_layout.addWidget(self.from_name)
        top_layout.addWidget(QLabel("From Email:"))
        top_layout.addWidget(self.from_email)
        
        self.cc_line = QLineEdit()
        self.bcc_line = QLineEdit()
        top_layout.addWidget(QLabel("CC:"))
        top_layout.addWidget(self.cc_line)
        top_layout.addWidget(QLabel("BCC:"))
        top_layout.addWidget(self.bcc_line)
        
        main_layout.addLayout(top_layout)

        # --- Composing and Preview Pane ---
        compose_pane = QWidget()
        compose_layout = QVBoxLayout(compose_pane)
        compose_layout.addWidget(QLabel("Subject:"))
        self.subject_line = QLineEdit()
        compose_layout.addWidget(self.subject_line)
        compose_layout.addWidget(QLabel("Message Body (use {ColumnName} for variables):"))
        self.body_text = QTextEdit()
        compose_layout.addWidget(self.body_text)

        preview_pane = QWidget()
        preview_layout = QVBoxLayout(preview_pane)
        preview_box = QGroupBox("Email Preview")
        preview_box_layout = QVBoxLayout(preview_box)

        self.preview_panel = QWebEngineView()
        preview_box_layout.addWidget(self.preview_panel)
        
        nav_layout = QHBoxLayout()
        self.prev_btn = QPushButton("<")
        self.next_btn = QPushButton(">")
        self.preview_status_label = QLabel("No preview available.")
        nav_layout.addWidget(self.prev_btn)
        nav_layout.addWidget(self.preview_status_label, 1)
        nav_layout.addWidget(self.next_btn)
        preview_box_layout.addLayout(nav_layout)
        preview_layout.addWidget(preview_box)

        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)

        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(compose_pane)
        splitter.addWidget(preview_pane)
        splitter.setSizes([int(self.width() * 0.4), int(self.width() * 0.6)])
        main_layout.addWidget(splitter)
        
        # --- Connections ---
        self.subject_line.textChanged.connect(self.update_preview)
        self.body_text.textChanged.connect(self.update_preview)
        self.prev_btn.clicked.connect(self.preview_previous)
        self.next_btn.clicked.connect(self.preview_next)
        
        send_layout = QHBoxLayout()
        self.test_email_btn = QPushButton("Send Test Email")
        self.test_email_btn.clicked.connect(self.send_test_email)
        self.send_btn = QPushButton("Send Campaign")
        self.send_btn.clicked.connect(self.start_sending)
        send_layout.addWidget(self.test_email_btn)
        send_layout.addWidget(self.send_btn)
        
        main_layout.addLayout(send_layout)
        self.tabs.addTab(tab_compose, "Compose & Preview")

    def create_recipients_tab(self):
        tab_recipients = QWidget()
        layout = QVBoxLayout(tab_recipients)
        
        csv_buttons_layout = QHBoxLayout()
        load_csvs_btn = QPushButton("Load CSV(s)")
        load_csvs_btn.clicked.connect(self.load_csvs)
        clear_list_btn = QPushButton("Clear Recipient List")
        clear_list_btn.clicked.connect(self.clear_recipient_list)
        self.recipients_count_label = QLabel("Total Unique Recipients: 0")
        csv_buttons_layout.addWidget(load_csvs_btn)
        csv_buttons_layout.addWidget(clear_list_btn)
        csv_buttons_layout.addWidget(self.recipients_count_label)
        
        layout.addLayout(csv_buttons_layout)
        
        layout.addWidget(QLabel("Recipient List (one email per line):"))
        self.recipients_list_edit = QPlainTextEdit()
        self.recipients_list_edit.setPlaceholderText("Paste emails here, one per line. Duplicates will be removed automatically.")
        self.recipients_list_edit.textChanged.connect(self.update_recipient_data_from_text)
        layout.addWidget(self.recipients_list_edit)
        
        self.tabs.addTab(tab_recipients, "Recipients")

    def create_settings_tab(self):
        tab_settings = QWidget()
        layout = QVBoxLayout(tab_settings)
        
        self.smtp_host = QLineEdit()
        self.smtp_port = QLineEdit()
        self.smtp_user = QLineEdit()
        self.smtp_pass = QLineEdit()
        self.smtp_pass.setEchoMode(QLineEdit.Password)
        
        layout.addWidget(QLabel("SMTP Host:"))
        layout.addWidget(self.smtp_host)
        layout.addWidget(QLabel("SMTP Port:"))
        layout.addWidget(self.smtp_port)
        layout.addWidget(QLabel("Username:"))
        layout.addWidget(self.smtp_user)
        layout.addWidget(QLabel("Password:"))
        layout.addWidget(self.smtp_pass)
        
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        layout.addStretch()
        self.tabs.addTab(tab_settings, "Settings")

    def create_log_tab(self):
        tab_log = QWidget()
        layout = QVBoxLayout(tab_log)
        self.log_box = QPlainTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)
        self.tabs.addTab(tab_log, "Log")

    def load_settings(self):
        try:
            with open(CONFIG_FILE, 'r') as f:
                settings = json.load(f)
                self.smtp_host.setText(settings.get('host', ''))
                self.smtp_port.setText(settings.get('port', '587'))
                self.smtp_user.setText(settings.get('username', ''))
                self.smtp_pass.setText(settings.get('password', ''))
                self.from_name.setText(settings.get('from_name', 'Leon Marcel'))
                self.from_email.setText(settings.get('from_email', ''))
        except FileNotFoundError:
            self.log_box.appendPlainText("Settings file not found. Please configure and save settings.")

    def save_settings(self):
        settings = {
            'host': self.smtp_host.text(),
            'port': self.smtp_port.text(),
            'username': self.smtp_user.text(),
            'password': self.smtp_pass.text(),
            'from_name': self.from_name.text(),
            'from_email': self.from_email.text()
        }
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump(settings, f, indent=4)
            QMessageBox.information(self, "Success", "Settings saved successfully.")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not save settings: {e}")

    def load_csvs(self):
        paths, _ = QFileDialog.getOpenFileNames(self, "Load CSV Files", "", "CSV Files (*.csv)")
        if not paths:
            return

        # Store the loaded CSV paths for later use
        self.loaded_csv_paths = paths

        new_recipients_from_csvs = set()
        total_rows = 0

        for path in paths:
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        total_rows += 1
                        email = row.get('Email')
                        if email:
                            new_recipients_from_csvs.add(email.strip())
            except Exception as e:
                self.log_box.appendPlainText(f"Failed to load CSV: {path} - {e}")

        existing_emails = set([e.strip() for e in self.recipients_list_edit.toPlainText().split('\n') if e.strip()])

        combined_emails = list(existing_emails.union(new_recipients_from_csvs))

        self.recipients_list_edit.setPlainText('\n'.join(combined_emails))

        self.log_box.appendPlainText(f"Successfully loaded {len(new_recipients_from_csvs)} unique recipients from {len(paths)} CSV file(s).")
        

    def update_recipient_data_from_text(self):
        emails = self.recipients_list_edit.toPlainText().split('\n')
        unique_recipients = {}
        
        # Get all available columns from CSV files
        available_columns = set()
        for path in self.loaded_csv_paths:
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    available_columns.update(reader.fieldnames or [])
            except Exception:
                continue
        
        # Create mapping of email to their data from CSVs
        email_data_mapping = {}
        for path in self.loaded_csv_paths:
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if 'Email' in row and row['Email']:
                            email = row['Email'].lower().strip()
                            email_data_mapping[email] = row
            except Exception:
                continue
        
        # Process each email in the text area
        for email in emails:
            email = email.strip()
            if email and '@' in email:
                email_lower = email.lower()
                # Initialize with basic email data
                unique_recipients[email_lower] = {'Email': email}
                
                # Add data from CSV if available
                if email_lower in email_data_mapping:
                    unique_recipients[email_lower].update(email_data_mapping[email_lower])
                else:
                    # If no CSV data, initialize other columns with empty values
                    for column in available_columns:
                        if column != 'Email':
                            unique_recipients[email_lower][column] = ''
        
        self.recipients_data = list(unique_recipients.values())
        self.recipients_count_label.setText(f"Total Unique Recipients: {len(self.recipients_data)}")

        # Debug: Log the first recipient's data to see what columns are available
        if self.recipients_data:
            first_recipient = self.recipients_data[0]
            available_keys = list(first_recipient.keys())
            self.log_box.appendPlainText(f"DEBUG: Available columns for replacement: {available_keys}")
            self.log_box.appendPlainText(f"DEBUG: Sample data: {first_recipient}")

        self.update_preview()
        
    def clear_recipient_list(self):
        self.recipients_data = []
        self.loaded_csv_paths = []
        self.recipients_list_edit.clear()
        self.recipients_count_label.setText("Total Unique Recipients: 0")
        self.update_preview()

    def update_preview(self):
        if not self.recipients_data:
            self.preview_panel.setHtml("<p style='text-align: center; color: #888;'>Load a CSV or add recipients and write your message to see a preview.</p>")
            self.preview_status_label.setText("No customers loaded.")
            self.prev_btn.setEnabled(False)
            self.next_btn.setEnabled(False)
            return

        recipient_data = self.recipients_data[self.current_preview_index]
        
        personalized_subject = self.subject_line.text()
        personalized_body = self.body_text.toPlainText()

        for key, value in recipient_data.items():
            placeholder = f"{{{key}}}"
            personalized_subject = personalized_subject.replace(placeholder, str(value))
            personalized_body = personalized_body.replace(placeholder, str(value))
            
        try:
            with open('template.html', 'r', encoding='utf-8') as f:
                html_template = f.read()
            
            final_html = html_template.format(subject=personalized_subject, message_body=personalized_body.replace('\n', '<br>'))
            self.preview_panel.setHtml(final_html)
            
            current_email = recipient_data.get('Email', 'N/A')
            status_text = f"Previewing: {current_email} ({self.current_preview_index + 1} of {len(self.recipients_data)})"
            self.preview_status_label.setText(status_text)
            self.prev_btn.setEnabled(len(self.recipients_data) > 1)
            self.next_btn.setEnabled(len(self.recipients_data) > 1)

        except FileNotFoundError:
            self.preview_panel.setHtml("Error: 'template.html' not found.")
            self.log_box.appendPlainText("Error: 'template.html' not found.")
        except Exception as e:
            self.preview_panel.setHtml(f"Preview Error: {e}")

    def preview_next(self):
        if self.recipients_data:
            self.current_preview_index = min(self.current_preview_index + 1, len(self.recipients_data) - 1)
            self.update_preview()

    def preview_previous(self):
        if self.recipients_data:
            self.current_preview_index = max(self.current_preview_index - 1, 0)
            self.update_preview()
            
    def get_common_email_data(self):
        settings = {
            'host': self.smtp_host.text(),
            'port': self.smtp_port.text(),
            'username': self.smtp_user.text(),
            'password': self.smtp_pass.text()
        }
        
        email_data = {
            'from_name': self.from_name.text(),
            'from_email': self.from_email.text(),
            'subject': self.subject_line.text(),
            'body': self.body_text.toPlainText(),
        }
        
        if not all(settings.values()) or not all(email_data.values()):
             QMessageBox.warning(self, "Warning", "Please complete all fields in the Compose and Settings tabs.")
             return None, None
        email_data.update({
        'cc_list': [e.strip() for e in self.cc_line.text().split(',') if e.strip()],
        'bcc_list': [e.strip() for e in self.bcc_line.text().split(',') if e.strip()]
        })
        return settings, email_data
        
    def start_sending(self):
        if not self.recipients_data:
            QMessageBox.warning(self, "Warning", "No recipients loaded. Please go to the Recipients tab and add emails first.")
            return
        
        settings, email_data = self.get_common_email_data()
        if not settings:
            return

        self.send_btn.setEnabled(False)
        self.tabs.setCurrentIndex(3) # Switch to Log tab
        self.log_box.clear()
        
        self.worker = EmailWorker(settings, self.recipients_data, email_data, email_data['cc_list'], email_data['bcc_list'])
        self.worker.log_update.connect(self.log_box.appendPlainText)
        self.worker.finished.connect(self.on_finished)
        self.worker.start()

    def send_test_email(self):
        dialog = TestEmailDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            test_email = dialog.email_input.text().strip()
            if not test_email or '@' not in test_email:
                QMessageBox.warning(self, "Invalid Email", "Please enter a valid email address.")
                return

            settings, email_data = self.get_common_email_data()
            if not settings:
                return
            
            # Create dummy recipient data for personalization test
            test_data = {
                'Email': test_email,
                'Name': 'Test Recipient',
                'Billing Name': 'Test Recipient',
                'First Name': 'Test'
            }
            
            self.tabs.setCurrentIndex(3)
            self.log_box.clear()
            self.log_box.appendPlainText(f"Sending test email to: {test_email}...")
            
            test_worker = EmailWorker(settings, [test_data], email_data, email_data['cc_list'], email_data['bcc_list'])
            test_worker.log_update.connect(self.log_box.appendPlainText)
            test_worker.finished.connect(lambda msg: QMessageBox.information(self, "Test Email Result", msg))
            test_worker.start()


    def on_finished(self, result_message):
        QMessageBox.information(self, "Process Finished", result_message)
        self.send_btn.setEnabled(True)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())