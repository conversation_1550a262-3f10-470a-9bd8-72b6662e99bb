// QtWebEngineCoremod.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtWebEngineCore, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip
%Import QtNetwork/QtNetworkmod.sip

%Timeline {QtWebEngine_5_4_0 QtWebEngine_5_4_1 QtWebEngine_5_4_2 QtWebEngine_5_5_0 QtWebEngine_5_5_1 QtWebEngine_5_6_0 QtWebEngine_5_6_1 QtWebEngine_5_6_2 QtWebEngine_5_6_3 QtWebEngine_5_6_4 QtWebEngine_5_6_5 QtWebEngine_5_6_6 QtWebEngine_5_6_7 QtWebEngine_5_6_8 QtWebEngine_5_6_9 QtWebEngine_5_7_0 QtWebEngine_5_7_1 QtWebEngine_5_8_0 QtWebEngine_5_8_1 QtWebEngine_5_9_0 QtWebEngine_5_9_1 QtWebEngine_5_9_2 QtWebEngine_5_9_3 QtWebEngine_5_9_4 QtWebEngine_5_9_5 QtWebEngine_5_9_6 QtWebEngine_5_9_7 QtWebEngine_5_9_8 QtWebEngine_5_9_9 QtWebEngine_5_10_0 QtWebEngine_5_10_1 QtWebEngine_5_11_0 QtWebEngine_5_11_1 QtWebEngine_5_11_2 QtWebEngine_5_11_3 QtWebEngine_5_12_0 QtWebEngine_5_12_1 QtWebEngine_5_12_2 QtWebEngine_5_12_3 QtWebEngine_5_12_4 QtWebEngine_5_13_0 QtWebEngine_5_14_0 QtWebEngine_5_15_0}

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQtWebEngine.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper

%Include qwebengineclientcertificatestore.sip
%Include qwebenginecookiestore.sip
%Include qwebenginefindtextresult.sip
%Include qwebenginehttprequest.sip
%Include qwebenginenotification.sip
%Include qwebenginequotarequest.sip
%Include qwebengineregisterprotocolhandlerrequest.sip
%Include qwebengineurlrequestinfo.sip
%Include qwebengineurlrequestinterceptor.sip
%Include qwebengineurlrequestjob.sip
%Include qwebengineurlscheme.sip
%Include qwebengineurlschemehandler.sip
