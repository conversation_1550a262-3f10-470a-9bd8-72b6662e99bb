// qwebengineurlrequestinfo.sip generated by MetaSIP
//
// This file is part of the QtWebEngineCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_6_0 -)

class QWebEngineUrlRequestInfo
{
%TypeHeaderCode
#include <qwebengineurlrequestinfo.h>
%End

public:
    enum ResourceType
    {
        ResourceTypeMainFrame,
        ResourceTypeSubFrame,
        ResourceTypeStylesheet,
        ResourceTypeScript,
        ResourceTypeImage,
        ResourceTypeFontResource,
        ResourceTypeSubResource,
        ResourceTypeObject,
        ResourceTypeMedia,
        ResourceTypeWorker,
        ResourceTypeSharedWorker,
        ResourceTypePrefetch,
        ResourceTypeFavicon,
        ResourceTypeXhr,
        ResourceTypePing,
        ResourceTypeServiceWorker,
        ResourceTypeUnknown,
%If (QtWebEngine_5_7_0 -)
        ResourceTypeCspReport,
%End
%If (QtWebEngine_5_7_0 -)
        ResourceTypePluginResource,
%End
%If (QtWebEngine_5_14_0 -)
        ResourceTypeNavigationPreloadMainFrame,
%End
%If (QtWebEngine_5_14_0 -)
        ResourceTypeNavigationPreloadSubFrame,
%End
    };

    enum NavigationType
    {
        NavigationTypeLink,
        NavigationTypeTyped,
        NavigationTypeFormSubmitted,
        NavigationTypeBackForward,
        NavigationTypeReload,
%If (QtWebEngine_5_14_0 -)
        NavigationTypeRedirect,
%End
        NavigationTypeOther,
    };

    QWebEngineUrlRequestInfo::ResourceType resourceType() const;
    QWebEngineUrlRequestInfo::NavigationType navigationType() const;
    QUrl requestUrl() const;
    QUrl firstPartyUrl() const;
    QByteArray requestMethod() const;
    void block(bool shouldBlock);
    void redirect(const QUrl &url);
    void setHttpHeader(const QByteArray &name, const QByteArray &value);
%If (QtWebEngine_5_14_0 -)
    QUrl initiator() const;
%End

private:
%If (QtWebEngine_5_14_0 -)
    QWebEngineUrlRequestInfo();
%End
    QWebEngineUrlRequestInfo(const QWebEngineUrlRequestInfo &);
    ~QWebEngineUrlRequestInfo();
};

%End
