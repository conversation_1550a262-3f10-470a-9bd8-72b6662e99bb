// qwebenginecertificateerror.sip generated by MetaSIP
//
// This file is part of the QtWebEngineWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWebEngineCertificateError /NoDefaultCtors/
{
%TypeHeaderCode
#include <qwebenginecertificateerror.h>
%End

public:
    enum Error
    {
        SslPinnedKeyNotInCertificateChain,
        CertificateCommonNameInvalid,
        CertificateDateInvalid,
        CertificateAuthorityInvalid,
        CertificateContainsErrors,
        CertificateNoRevocationMechanism,
        CertificateUnableToCheckRevocation,
        CertificateRevoked,
        CertificateInvalid,
        CertificateWeakSignatureAlgorithm,
        CertificateNonUniqueName,
        CertificateWeakKey,
        CertificateNameConstraintViolation,
%If (QtWebEngine_5_7_1 -)
        CertificateValidityTooLong,
%End
%If (QtWebEngine_5_8_0 -)
        CertificateTransparencyRequired,
%End
%If (QtWebEngine_5_15_0 -)
        CertificateKnownInterceptionBlocked,
%End
    };

    QWebEngineCertificateError::Error error() const;
    QUrl url() const;
    bool isOverridable() const;
    QString errorDescription() const;
%If (QtWebEngine_5_14_0 -)
    QWebEngineCertificateError(const QWebEngineCertificateError &other);
%End

private:
%If (- QtWebEngine_5_14_0)
    QWebEngineCertificateError(const QWebEngineCertificateError &);
%End

public:
%If (QtWebEngine_5_14_0 -)
    void defer();
%End
%If (QtWebEngine_5_14_0 -)
    bool deferred() const;
%End
%If (QtWebEngine_5_14_0 -)
    void rejectCertificate();
%End
%If (QtWebEngine_5_14_0 -)
    void ignoreCertificateError();
%End
%If (QtWebEngine_5_14_0 -)
    bool answered() const;
%End
%If (QtWebEngine_5_14_0 -)
    QList<QSslCertificate> certificateChain() const;
%End
};
