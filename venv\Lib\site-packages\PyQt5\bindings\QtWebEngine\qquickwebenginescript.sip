// qquickwebenginescript.sip generated by MetaSIP
//
// This file is part of the QtWebEngine Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtWebEngine.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtWebEngine_5_9_0 -)

class QQuickWebEngineScript : public QObject
{
%TypeHeaderCode
#include <qquickwebenginescript.h>
%End

public:
    enum InjectionPoint
    {
        Deferred,
        DocumentReady,
        DocumentCreation,
    };

    enum ScriptWorldId
    {
        MainWorld,
        ApplicationWorld,
        UserWorld,
    };

    explicit QQuickWebEngineScript(QObject *parent /TransferThis/ = 0);
    virtual ~QQuickWebEngineScript();
    QString toString() const;
    QString name() const;
    QUrl sourceUrl() const;
    QString sourceCode() const;
    QQuickWebEngineScript::InjectionPoint injectionPoint() const;
    QQuickWebEngineScript::ScriptWorldId worldId() const;
    bool runOnSubframes() const;
    void setName(const QString &name);
    void setSourceUrl(const QUrl &url);
    void setSourceCode(const QString &code);
    void setInjectionPoint(QQuickWebEngineScript::InjectionPoint injectionPoint);
    void setWorldId(QQuickWebEngineScript::ScriptWorldId scriptWorldId);
    void setRunOnSubframes(bool on);

signals:
    void nameChanged(const QString &name);
    void sourceUrlChanged(const QUrl &url);
    void sourceCodeChanged(const QString &code);
    void injectionPointChanged(QQuickWebEngineScript::InjectionPoint injectionPoint /ScopesStripped=1/);
    void worldIdChanged(QQuickWebEngineScript::ScriptWorldId scriptWorldId /ScopesStripped=1/);
    void runOnSubframesChanged(bool on);

protected:
    virtual void timerEvent(QTimerEvent *e);
};

%End
