// qdialogbuttonbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDialogButtonBox : public QWidget
{
%TypeHeaderCode
#include <qdialogbuttonbox.h>
%End

public:
    enum ButtonLayout
    {
        WinLayout,
        <PERSON>Layout,
        KdeLayout,
        GnomeLayout,
%If (Qt_5_10_0 -)
        AndroidLayout,
%End
    };

    enum ButtonRole
    {
        InvalidRole,
        AcceptRole,
        RejectRole,
        DestructiveRole,
        ActionRole,
        HelpRole,
        YesRole,
        NoRole,
        ResetRole,
        ApplyRole,
    };

    enum StandardButton
    {
        NoButton,
        Ok,
        Save,
        SaveAll,
        Open,
        Yes,
        YesToAll,
        No,
        NoToAll,
        Abort,
        Retry,
        Ignore,
        Close,
        Cancel,
        Discard,
        Help,
        Apply,
        Reset,
        RestoreDefaults,
    };

    typedef QFlags<QDialogButtonBox::StandardButton> StandardButtons;
    QDialogButtonBox(QWidget *parent /TransferThis/ = 0);
    QDialogButtonBox(Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
%If (Qt_5_2_0 -)
    QDialogButtonBox(QDialogButtonBox::StandardButtons buttons, QWidget *parent /TransferThis/ = 0);
%End
%If (Qt_5_2_0 -)
    QDialogButtonBox(QDialogButtonBox::StandardButtons buttons, Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
%End
%If (- Qt_5_2_0)
    QDialogButtonBox(QFlags<QDialogButtonBox::StandardButton> buttons, Qt::Orientation orientation = Qt::Horizontal, QWidget *parent /TransferThis/ = 0);
%End
    virtual ~QDialogButtonBox();
    void setOrientation(Qt::Orientation orientation);
    Qt::Orientation orientation() const;
    void addButton(QAbstractButton *button /Transfer/, QDialogButtonBox::ButtonRole role);
    QPushButton *addButton(const QString &text, QDialogButtonBox::ButtonRole role) /Transfer/;
    QPushButton *addButton(QDialogButtonBox::StandardButton button) /Transfer/;
    void removeButton(QAbstractButton *button /TransferBack/);
    void clear();
    QList<QAbstractButton *> buttons() const;
    QDialogButtonBox::ButtonRole buttonRole(QAbstractButton *button) const;
    void setStandardButtons(QDialogButtonBox::StandardButtons buttons);
    QDialogButtonBox::StandardButtons standardButtons() const;
    QDialogButtonBox::StandardButton standardButton(QAbstractButton *button) const;
    QPushButton *button(QDialogButtonBox::StandardButton which) const;
    void setCenterButtons(bool center);
    bool centerButtons() const;

signals:
    void accepted();
    void clicked(QAbstractButton *button);
    void helpRequested();
    void rejected();

protected:
    virtual void changeEvent(QEvent *event);
    virtual bool event(QEvent *event);
};

QFlags<QDialogButtonBox::StandardButton> operator|(QDialogButtonBox::StandardButton f1, QFlags<QDialogButtonBox::StandardButton> f2);
